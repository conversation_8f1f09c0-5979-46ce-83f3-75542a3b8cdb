"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(request) {\n    try {\n        let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n            request: {\n                headers: request.headers\n            }\n        });\n        // Skip middleware for static files and API routes\n        const { pathname } = request.nextUrl;\n        if (pathname.startsWith('/_next/') || pathname.startsWith('/api/') || pathname.includes('.')) {\n            return response;\n        }\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://wbzlnrekvqjkjvxkohwf.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndiemxucmVrdnFqa2p2eGtvaHdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDE2NDksImV4cCI6MjA2MjkxNzY0OX0.ahMF1_RXeu-D6jJ6EF8TNzl-zJRSdy-yqwB4wWzL98Y\", {\n            cookies: {\n                get (name) {\n                    return request.cookies.get(name)?.value;\n                },\n                set (name, value, options) {\n                    request.cookies.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                    response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                        request: {\n                            headers: request.headers\n                        }\n                    });\n                    response.cookies.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                },\n                remove (name, options) {\n                    request.cookies.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                    response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                        request: {\n                            headers: request.headers\n                        }\n                    });\n                    response.cookies.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                }\n            }\n        });\n        let user = null;\n        try {\n            const { data, error } = await supabase.auth.getUser();\n            if (!error && data) {\n                user = data.user;\n            }\n        } catch (authError) {\n            // Silently handle auth errors and continue without blocking\n            console.warn('Auth check failed in middleware, continuing without auth:', authError);\n        }\n        // Define protected routes\n        const protectedRoutes = [\n            '/post',\n            '/drafts',\n            '/profile',\n            '/notifications',\n            '/settings'\n        ];\n        // Define auth routes (should redirect to home if already logged in)\n        const authRoutes = [\n            '/auth/login',\n            '/auth/signup'\n        ];\n        // Check if current path is a protected route\n        const isProtectedRoute = protectedRoutes.some((route)=>pathname.startsWith(route));\n        const isAuthRoute = authRoutes.some((route)=>pathname.startsWith(route));\n        // If user is not logged in and trying to access protected route\n        if (!user && isProtectedRoute) {\n            const redirectUrl = new URL('/auth/login', request.url);\n            redirectUrl.searchParams.set('redirectTo', pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        // If user is logged in and trying to access auth routes, redirect to home\n        if (user && isAuthRoute) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/', request.url));\n        }\n        return response;\n    } catch (error) {\n        console.error('Middleware error:', error);\n        // Always return a response, never block the request\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    }\n}\n// Temporarily disable middleware to fix loading issues\nconst config = {\n    matcher: []\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});