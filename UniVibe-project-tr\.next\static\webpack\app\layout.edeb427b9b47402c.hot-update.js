"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"616845ae6029\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2MTY4NDVhZTYwMjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseBrowserClient)();\n    const refreshProfile = async ()=>{\n        try {\n            const { data: { user: currentUser } } = await supabase.auth.getUser();\n            if (currentUser) {\n                const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(currentUser.id);\n                if (data && !error) {\n                    setProfile(data);\n                }\n            }\n        } catch (error) {\n            console.error('Error refreshing profile:', error);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            const { error } = await supabase.auth.signOut();\n            if (!error) {\n                setUser(null);\n                setProfile(null);\n            }\n        } catch (error) {\n            console.error('Sign out error:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            let mounted = true;\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        console.log('Initializing auth...');\n                        // Get initial session\n                        const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n                        if (sessionError) {\n                            console.error('Session error:', sessionError);\n                            if (mounted) {\n                                setUser(null);\n                                setProfile(null);\n                                setLoading(false);\n                            }\n                            return;\n                        }\n                        if (!mounted) return;\n                        console.log('Session:', (session === null || session === void 0 ? void 0 : session.user) ? 'User found' : 'No user');\n                        var _session_user;\n                        setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                        // Get user profile if user exists\n                        if (session === null || session === void 0 ? void 0 : session.user) {\n                            try {\n                                const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(session.user.id);\n                                if (data && !error && mounted) {\n                                    console.log('Profile loaded:', data.full_name);\n                                    setProfile(data);\n                                } else if (error) {\n                                    var _error_message;\n                                    console.error('Profile error:', error);\n                                    // If profile doesn't exist, create one\n                                    if (error.code === 'PGRST116' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('No rows returned'))) {\n                                        console.log('Creating new profile for user...');\n                                        try {\n                                            const { data: newProfile, error: createError } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.createUserProfile)(session.user);\n                                            if (newProfile && !createError && mounted) {\n                                                console.log('New profile created:', newProfile.full_name);\n                                                setProfile(newProfile);\n                                            } else if (createError) {\n                                                console.error('Error creating profile:', createError);\n                                            }\n                                        } catch (createProfileError) {\n                                            console.error('Error creating profile:', createProfileError);\n                                        }\n                                    }\n                                }\n                            } catch (profileError) {\n                                console.error('Profile error:', profileError);\n                            }\n                        } else {\n                            setProfile(null);\n                        }\n                        if (mounted) {\n                            console.log('Auth initialization complete');\n                            setLoading(false);\n                        }\n                    } catch (error) {\n                        console.error('Auth initialization error:', error);\n                        if (mounted) {\n                            setUser(null);\n                            setProfile(null);\n                            setLoading(false);\n                        }\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n            // Fallback timeout to ensure loading doesn't persist indefinitely\n            const timeoutId = setTimeout({\n                \"AuthProvider.useEffect.timeoutId\": ()=>{\n                    if (mounted) {\n                        console.log('Auth timeout - forcing loading to false');\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.timeoutId\"], 5000) // 5 second timeout\n            ;\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    if (!mounted) return;\n                    console.log('Auth state changed:', event);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        try {\n                            const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(session.user.id);\n                            if (data && !error && mounted) {\n                                setProfile(data);\n                            } else if (error) {\n                                var _error_message;\n                                console.error('Profile error on auth change:', error);\n                                // If profile doesn't exist, create one\n                                if (error.code === 'PGRST116' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('No rows returned'))) {\n                                    console.log('Creating new profile for user on auth change...');\n                                    try {\n                                        const { data: newProfile, error: createError } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.createUserProfile)(session.user);\n                                        if (newProfile && !createError && mounted) {\n                                            console.log('New profile created on auth change:', newProfile.full_name);\n                                            setProfile(newProfile);\n                                        } else if (createError) {\n                                            console.error('Error creating profile on auth change:', createError);\n                                        }\n                                    } catch (createProfileError) {\n                                        console.error('Error creating profile on auth change:', createProfileError);\n                                    }\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error getting user profile on auth change:', error);\n                        }\n                    } else {\n                        setProfile(null);\n                    }\n                    if (mounted) {\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    mounted = false;\n                    clearTimeout(timeoutId);\n                    subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            profile,\n            loading,\n            signOut,\n            refreshProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"DYSpA4ZauWKW8e4CNkO4ayA+RbM=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth-context.tsx\n"));

/***/ })

});