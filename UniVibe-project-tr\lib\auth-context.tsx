"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { createSupabaseBrowserClient, getUserProfile, createUserProfile, UserRole, forceSignOut } from './supabase'

interface UserProfile {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: UserRole
  university: string | null
  created_at: string | null
  updated_at: string | null
}

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseBrowserClient()

  const refreshProfile = async () => {
    try {
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      if (currentUser) {
        const { data, error } = await getUserProfile(currentUser.id)
        if (data && !error) {
          setProfile(data as UserProfile)
        } else if (error) {
          console.error('Error refreshing profile:', error)
        }
      }
    } catch (error) {
      console.error('Error refreshing profile:', error)
    }
  }

  const signOut = async () => {
    try {
      console.log('Signing out user...')
      const { error } = await forceSignOut()
      if (!error) {
        setUser(null)
        setProfile(null)
        console.log('Sign out successful, redirecting to login...')
        // Force redirect to login page after sign out
        window.location.href = '/auth/login'
      } else {
        console.error('Sign out error:', error)
      }
    } catch (error) {
      console.error('Sign out error:', error)
      // Even if there's an error, clear local state and redirect
      setUser(null)
      setProfile(null)
      window.location.href = '/auth/login'
    }
  }

  useEffect(() => {
    let mounted = true

    const initializeAuth = async () => {
      try {
        console.log('Initializing auth...')
        // Get initial session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error('Session error:', sessionError)
          if (mounted) {
            setUser(null)
            setProfile(null)
            setLoading(false)
          }
          return
        }

        if (!mounted) return

        console.log('Session:', session?.user ? 'User found' : 'No user')
        setUser(session?.user ?? null)

        // Get user profile if user exists
        if (session?.user) {
          await handleUserProfile(session.user, mounted)
        } else {
          setProfile(null)
        }

        if (mounted) {
          console.log('Auth initialization complete')
          setLoading(false)
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        if (mounted) {
          setUser(null)
          setProfile(null)
          setLoading(false)
        }
      }
    }

    const handleUserProfile = async (user: User, isMounted: boolean) => {
      try {
        const { data, error } = await getUserProfile(user.id)
        if (data && !error && isMounted) {
          console.log('Profile loaded:', data.full_name)
          setProfile(data as UserProfile)
        } else if (error) {
          console.error('Profile error details:', {
            code: error.code,
            message: error.message,
            details: error.details,
            hint: error.hint
          })

          // If profile doesn't exist, try to create one
          if (error.code === 'PGRST116' || error.message?.includes('No rows returned')) {
            console.log('Profile not found in database, creating new profile...')
            try {
              const { data: newProfile, error: createError } = await createUserProfile(user)
              if (newProfile && !createError && isMounted) {
                console.log('New profile created:', newProfile.full_name)
                setProfile(newProfile as UserProfile)
              } else if (createError) {
                console.error('Error creating profile details:', {
                  code: createError.code,
                  message: createError.message,
                  details: createError.details,
                  hint: createError.hint
                })
                // If we can't create a profile, sign out the user
                console.log('Cannot create profile, signing out user...')
                await supabase.auth.signOut()
                setUser(null)
                setProfile(null)
                window.location.href = '/auth/login'
              }
            } catch (createProfileError) {
              console.error('Exception creating profile:', createProfileError)
              // If we can't create a profile, sign out the user
              console.log('Exception creating profile, signing out user...')
              await supabase.auth.signOut()
              setUser(null)
              setProfile(null)
              window.location.href = '/auth/login'
            }
          } else {
            // For other errors, also sign out the user
            console.log('Database error, signing out user...')
            await supabase.auth.signOut()
            setUser(null)
            setProfile(null)
            window.location.href = '/auth/login'
          }
        }
      } catch (profileError) {
        console.error('Exception getting profile:', profileError)
        // For any exception, sign out the user
        console.log('Exception getting profile, signing out user...')
        await supabase.auth.signOut()
        setUser(null)
        setProfile(null)
        window.location.href = '/auth/login'
      }
    }

    initializeAuth()

    // Fallback timeout to ensure loading doesn't persist indefinitely
    const timeoutId = setTimeout(() => {
      if (mounted) {
        console.log('Auth timeout - forcing loading to false')
        setLoading(false)
      }
    }, 5000) // 5 second timeout

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return

        console.log('Auth state changed:', event)
        setUser(session?.user ?? null)

        if (session?.user) {
          await handleUserProfile(session.user, mounted)
        } else {
          setProfile(null)
        }

        if (mounted) {
          setLoading(false)
        }
      }
    )

    return () => {
      mounted = false
      clearTimeout(timeoutId)
      subscription.unsubscribe()
    }
  }, [])

  return (
    <AuthContext.Provider value={{ user, profile, loading, signOut, refreshProfile }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
