"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"99443306339f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5OTQ0MzMwNjMzOWZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseBrowserClient)();\n    const refreshProfile = async ()=>{\n        try {\n            const { data: { user: currentUser } } = await supabase.auth.getUser();\n            if (currentUser) {\n                const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(currentUser.id);\n                if (data && !error) {\n                    setProfile(data);\n                }\n            }\n        } catch (error) {\n            console.error('Error refreshing profile:', error);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            const { error } = await supabase.auth.signOut();\n            if (!error) {\n                setUser(null);\n                setProfile(null);\n            }\n        } catch (error) {\n            console.error('Sign out error:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            let mounted = true;\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        console.log('Initializing auth...');\n                        // Get initial session\n                        const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n                        if (sessionError) {\n                            console.error('Session error:', sessionError);\n                            if (mounted) {\n                                setUser(null);\n                                setProfile(null);\n                                setLoading(false);\n                            }\n                            return;\n                        }\n                        if (!mounted) return;\n                        console.log('Session:', (session === null || session === void 0 ? void 0 : session.user) ? 'User found' : 'No user');\n                        var _session_user;\n                        setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                        // Get user profile if user exists\n                        if (session === null || session === void 0 ? void 0 : session.user) {\n                            try {\n                                const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(session.user.id);\n                                if (data && !error && mounted) {\n                                    console.log('Profile loaded:', data.full_name);\n                                    setProfile(data);\n                                } else if (error) {\n                                    var _error_message;\n                                    console.error('Profile error:', error);\n                                    // If profile doesn't exist, create one\n                                    if (error.code === 'PGRST116' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('No rows returned'))) {\n                                        console.log('Creating new profile for user...');\n                                        try {\n                                            const { data: newProfile, error: createError } = await createUserProfile(session.user);\n                                            if (newProfile && !createError && mounted) {\n                                                console.log('New profile created:', newProfile.full_name);\n                                                setProfile(newProfile);\n                                            } else if (createError) {\n                                                console.error('Error creating profile:', createError);\n                                            }\n                                        } catch (createProfileError) {\n                                            console.error('Error creating profile:', createProfileError);\n                                        }\n                                    }\n                                }\n                            } catch (profileError) {\n                                console.error('Profile error:', profileError);\n                            }\n                        } else {\n                            setProfile(null);\n                        }\n                        if (mounted) {\n                            console.log('Auth initialization complete');\n                            setLoading(false);\n                        }\n                    } catch (error) {\n                        console.error('Auth initialization error:', error);\n                        if (mounted) {\n                            setUser(null);\n                            setProfile(null);\n                            setLoading(false);\n                        }\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n            // Fallback timeout to ensure loading doesn't persist indefinitely\n            const timeoutId = setTimeout({\n                \"AuthProvider.useEffect.timeoutId\": ()=>{\n                    if (mounted) {\n                        console.log('Auth timeout - forcing loading to false');\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.timeoutId\"], 5000) // 5 second timeout\n            ;\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    if (!mounted) return;\n                    console.log('Auth state changed:', event);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        try {\n                            const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(session.user.id);\n                            if (data && !error && mounted) {\n                                setProfile(data);\n                            } else if (error) {\n                                var _error_message;\n                                console.error('Profile error on auth change:', error);\n                                // If profile doesn't exist, create one\n                                if (error.code === 'PGRST116' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('No rows returned'))) {\n                                    console.log('Creating new profile for user on auth change...');\n                                    try {\n                                        const { data: newProfile, error: createError } = await createUserProfile(session.user);\n                                        if (newProfile && !createError && mounted) {\n                                            console.log('New profile created on auth change:', newProfile.full_name);\n                                            setProfile(newProfile);\n                                        } else if (createError) {\n                                            console.error('Error creating profile on auth change:', createError);\n                                        }\n                                    } catch (createProfileError) {\n                                        console.error('Error creating profile on auth change:', createProfileError);\n                                    }\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error getting user profile on auth change:', error);\n                        }\n                    } else {\n                        setProfile(null);\n                    }\n                    if (mounted) {\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    mounted = false;\n                    clearTimeout(timeoutId);\n                    subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            profile,\n            loading,\n            signOut,\n            refreshProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"DYSpA4ZauWKW8e4CNkO4ayA+RbM=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth-context.tsx\n"));

/***/ })

});