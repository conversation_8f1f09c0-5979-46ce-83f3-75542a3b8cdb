"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { createSupabaseBrowserClient } from '@/lib/supabase'

export default function ClearAuthPage() {
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const supabase = createSupabaseBrowserClient()

  const clearAllAuth = async () => {
    setLoading(true)
    setError('')
    setMessage('')

    try {
      // First, clear the users table
      console.log('Clearing users table...')
      const { error: usersError } = await supabase
        .from('users')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all users

      if (usersError) {
        console.error('Error clearing users table:', usersError)
        setError(`Error clearing users table: ${usersError.message}`)
        return
      }

      // Clear other related tables
      console.log('Clearing related tables...')
      await supabase.from('user_favorite_categories').delete().neq('user_id', '00000000-0000-0000-0000-000000000000')
      await supabase.from('notifications').delete().neq('user_id', '00000000-0000-0000-0000-000000000000')
      await supabase.from('rsvps').delete().neq('user_id', '00000000-0000-0000-0000-000000000000')
      await supabase.from('events').delete().neq('organizer_id', '00000000-0000-0000-0000-000000000000')

      // Sign out current user
      console.log('Signing out current user...')
      await supabase.auth.signOut()

      // Clear browser storage
      if (typeof window !== 'undefined') {
        localStorage.clear()
        sessionStorage.clear()
        
        // Clear cookies
        document.cookie.split(";").forEach((c) => {
          const eqPos = c.indexOf("=")
          const name = eqPos > -1 ? c.substr(0, eqPos) : c
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname
        })
      }

      setMessage('All authentication data cleared successfully! You can now create fresh accounts.')
      
      // Redirect to login after a delay
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 3000)

    } catch (error) {
      console.error('Error clearing auth data:', error)
      setError(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-red-600">Clear All Auth Data</CardTitle>
          <CardDescription>
            This will remove all users and authentication data from the database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {message && (
            <Alert>
              <AlertDescription className="text-green-600">{message}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">⚠️ Warning</h4>
              <p className="text-sm text-yellow-700">
                This action will:
              </p>
              <ul className="text-sm text-yellow-700 mt-2 list-disc list-inside space-y-1">
                <li>Delete all user profiles from the database</li>
                <li>Clear all events, RSVPs, and notifications</li>
                <li>Sign out the current user</li>
                <li>Clear all browser storage</li>
                <li>Redirect to login page</li>
              </ul>
              <p className="text-sm text-yellow-700 mt-2 font-medium">
                Note: Users in Supabase Auth will remain but can be ignored.
              </p>
            </div>

            <Button
              onClick={clearAllAuth}
              disabled={loading}
              variant="destructive"
              className="w-full"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Clearing...
                </div>
              ) : (
                'Clear All Auth Data'
              )}
            </Button>

            <div className="text-center">
              <Button variant="outline" asChild>
                <a href="/auth/login">Cancel - Go to Login</a>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
