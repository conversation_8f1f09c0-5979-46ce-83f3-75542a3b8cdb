"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"43852f478164\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0Mzg1MmY0NzgxNjRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseBrowserClient)();\n    const refreshProfile = async ()=>{\n        try {\n            const { data: { user: currentUser } } = await supabase.auth.getUser();\n            if (currentUser) {\n                const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(currentUser.id);\n                if (data && !error) {\n                    setProfile(data);\n                }\n            }\n        } catch (error) {\n            console.error('Error refreshing profile:', error);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            const { error } = await supabase.auth.signOut();\n            if (!error) {\n                setUser(null);\n                setProfile(null);\n            }\n        } catch (error) {\n            console.error('Sign out error:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            let mounted = true;\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        console.log('Initializing auth...');\n                        // Get initial session\n                        const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n                        if (sessionError) {\n                            console.error('Session error:', sessionError);\n                            if (mounted) {\n                                setUser(null);\n                                setProfile(null);\n                                setLoading(false);\n                            }\n                            return;\n                        }\n                        if (!mounted) return;\n                        console.log('Session:', (session === null || session === void 0 ? void 0 : session.user) ? 'User found' : 'No user');\n                        var _session_user;\n                        setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                        // Get user profile if user exists\n                        if (session === null || session === void 0 ? void 0 : session.user) {\n                            try {\n                                const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(session.user.id);\n                                if (data && !error && mounted) {\n                                    console.log('Profile loaded:', data.full_name);\n                                    setProfile(data);\n                                } else if (error) {\n                                    console.error('Profile error:', error);\n                                }\n                            } catch (profileError) {\n                                console.error('Profile error:', profileError);\n                            }\n                        } else {\n                            setProfile(null);\n                        }\n                        if (mounted) {\n                            console.log('Auth initialization complete');\n                            setLoading(false);\n                        }\n                    } catch (error) {\n                        console.error('Auth initialization error:', error);\n                        if (mounted) {\n                            setUser(null);\n                            setProfile(null);\n                            setLoading(false);\n                        }\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    if (!mounted) return;\n                    console.log('Auth state changed:', event);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        try {\n                            const { data, error } = await (0,_supabase__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(session.user.id);\n                            if (data && !error && mounted) {\n                                setProfile(data);\n                            }\n                        } catch (error) {\n                            console.error('Error getting user profile on auth change:', error);\n                        }\n                    } else {\n                        setProfile(null);\n                    }\n                    if (mounted) {\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    mounted = false;\n                    subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            profile,\n            loading,\n            signOut,\n            refreshProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"DYSpA4ZauWKW8e4CNkO4ayA+RbM=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9hdXRoLWNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXNFO0FBRVk7QUFxQmxGLE1BQU1NLDRCQUFjTixvREFBYUEsQ0FBOEJPO0FBRXhELFNBQVNDLGFBQWEsS0FBMkM7UUFBM0MsRUFBRUMsUUFBUSxFQUFpQyxHQUEzQzs7SUFDM0IsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdSLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ1MsU0FBU0MsV0FBVyxHQUFHViwrQ0FBUUEsQ0FBcUI7SUFDM0QsTUFBTSxDQUFDVyxTQUFTQyxXQUFXLEdBQUdaLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU1hLFdBQVdaLHNFQUEyQkE7SUFFNUMsTUFBTWEsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRixNQUFNLEVBQUVDLE1BQU0sRUFBRVIsTUFBTVMsV0FBVyxFQUFFLEVBQUUsR0FBRyxNQUFNSCxTQUFTSSxJQUFJLENBQUNDLE9BQU87WUFDbkUsSUFBSUYsYUFBYTtnQkFDZixNQUFNLEVBQUVELElBQUksRUFBRUksS0FBSyxFQUFFLEdBQUcsTUFBTWpCLHlEQUFjQSxDQUFDYyxZQUFZSSxFQUFFO2dCQUMzRCxJQUFJTCxRQUFRLENBQUNJLE9BQU87b0JBQ2xCVCxXQUFXSztnQkFDYjtZQUNGO1FBQ0YsRUFBRSxPQUFPSSxPQUFPO1lBQ2RFLFFBQVFGLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzdDO0lBQ0Y7SUFFQSxNQUFNRyxVQUFVO1FBQ2QsSUFBSTtZQUNGLE1BQU0sRUFBRUgsS0FBSyxFQUFFLEdBQUcsTUFBTU4sU0FBU0ksSUFBSSxDQUFDSyxPQUFPO1lBQzdDLElBQUksQ0FBQ0gsT0FBTztnQkFDVlgsUUFBUTtnQkFDUkUsV0FBVztZQUNiO1FBQ0YsRUFBRSxPQUFPUyxPQUFPO1lBQ2RFLFFBQVFGLEtBQUssQ0FBQyxtQkFBbUJBO1FBQ25DO0lBQ0Y7SUFFQXBCLGdEQUFTQTtrQ0FBQztZQUNSLElBQUl3QixVQUFVO1lBRWQsTUFBTUM7eURBQWlCO29CQUNyQixJQUFJO3dCQUNGSCxRQUFRSSxHQUFHLENBQUM7d0JBQ1osc0JBQXNCO3dCQUN0QixNQUFNLEVBQUVWLE1BQU0sRUFBRVcsT0FBTyxFQUFFLEVBQUVQLE9BQU9RLFlBQVksRUFBRSxHQUFHLE1BQU1kLFNBQVNJLElBQUksQ0FBQ1csVUFBVTt3QkFFakYsSUFBSUQsY0FBYzs0QkFDaEJOLFFBQVFGLEtBQUssQ0FBQyxrQkFBa0JROzRCQUNoQyxJQUFJSixTQUFTO2dDQUNYZixRQUFRO2dDQUNSRSxXQUFXO2dDQUNYRSxXQUFXOzRCQUNiOzRCQUNBO3dCQUNGO3dCQUVBLElBQUksQ0FBQ1csU0FBUzt3QkFFZEYsUUFBUUksR0FBRyxDQUFDLFlBQVlDLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU25CLElBQUksSUFBRyxlQUFlOzRCQUMvQ21CO3dCQUFSbEIsUUFBUWtCLENBQUFBLGdCQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNuQixJQUFJLGNBQWJtQiwyQkFBQUEsZ0JBQWlCO3dCQUV6QixrQ0FBa0M7d0JBQ2xDLElBQUlBLG9CQUFBQSw4QkFBQUEsUUFBU25CLElBQUksRUFBRTs0QkFDakIsSUFBSTtnQ0FDRixNQUFNLEVBQUVRLElBQUksRUFBRUksS0FBSyxFQUFFLEdBQUcsTUFBTWpCLHlEQUFjQSxDQUFDd0IsUUFBUW5CLElBQUksQ0FBQ2EsRUFBRTtnQ0FDNUQsSUFBSUwsUUFBUSxDQUFDSSxTQUFTSSxTQUFTO29DQUM3QkYsUUFBUUksR0FBRyxDQUFDLG1CQUFtQlYsS0FBS2MsU0FBUztvQ0FDN0NuQixXQUFXSztnQ0FDYixPQUFPLElBQUlJLE9BQU87b0NBQ2hCRSxRQUFRRixLQUFLLENBQUMsa0JBQWtCQTtnQ0FDbEM7NEJBQ0YsRUFBRSxPQUFPVyxjQUFjO2dDQUNyQlQsUUFBUUYsS0FBSyxDQUFDLGtCQUFrQlc7NEJBQ2xDO3dCQUNGLE9BQU87NEJBQ0xwQixXQUFXO3dCQUNiO3dCQUVBLElBQUlhLFNBQVM7NEJBQ1hGLFFBQVFJLEdBQUcsQ0FBQzs0QkFDWmIsV0FBVzt3QkFDYjtvQkFDRixFQUFFLE9BQU9PLE9BQU87d0JBQ2RFLFFBQVFGLEtBQUssQ0FBQyw4QkFBOEJBO3dCQUM1QyxJQUFJSSxTQUFTOzRCQUNYZixRQUFROzRCQUNSRSxXQUFXOzRCQUNYRSxXQUFXO3dCQUNiO29CQUNGO2dCQUNGOztZQUVBWTtZQUVBLDBCQUEwQjtZQUMxQixNQUFNLEVBQUVULE1BQU0sRUFBRWdCLFlBQVksRUFBRSxFQUFFLEdBQUdsQixTQUFTSSxJQUFJLENBQUNlLGlCQUFpQjswQ0FDaEUsT0FBT0MsT0FBT1A7b0JBQ1osSUFBSSxDQUFDSCxTQUFTO29CQUVkRixRQUFRSSxHQUFHLENBQUMsdUJBQXVCUTt3QkFDM0JQO29CQUFSbEIsUUFBUWtCLENBQUFBLGdCQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNuQixJQUFJLGNBQWJtQiwyQkFBQUEsZ0JBQWlCO29CQUV6QixJQUFJQSxvQkFBQUEsOEJBQUFBLFFBQVNuQixJQUFJLEVBQUU7d0JBQ2pCLElBQUk7NEJBQ0YsTUFBTSxFQUFFUSxJQUFJLEVBQUVJLEtBQUssRUFBRSxHQUFHLE1BQU1qQix5REFBY0EsQ0FBQ3dCLFFBQVFuQixJQUFJLENBQUNhLEVBQUU7NEJBQzVELElBQUlMLFFBQVEsQ0FBQ0ksU0FBU0ksU0FBUztnQ0FDN0JiLFdBQVdLOzRCQUNiO3dCQUNGLEVBQUUsT0FBT0ksT0FBTzs0QkFDZEUsUUFBUUYsS0FBSyxDQUFDLDhDQUE4Q0E7d0JBQzlEO29CQUNGLE9BQU87d0JBQ0xULFdBQVc7b0JBQ2I7b0JBRUEsSUFBSWEsU0FBUzt3QkFDWFgsV0FBVztvQkFDYjtnQkFDRjs7WUFHRjswQ0FBTztvQkFDTFcsVUFBVTtvQkFDVlEsYUFBYUcsV0FBVztnQkFDMUI7O1FBQ0Y7aUNBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDL0IsWUFBWWdDLFFBQVE7UUFBQ0MsT0FBTztZQUFFN0I7WUFBTUU7WUFBU0U7WUFBU1c7WUFBU1I7UUFBZTtrQkFDNUVSOzs7Ozs7QUFHUDtHQS9IZ0JEO0tBQUFBO0FBaUlULFNBQVNnQzs7SUFDZCxNQUFNQyxVQUFVeEMsaURBQVVBLENBQUNLO0lBQzNCLElBQUltQyxZQUFZbEMsV0FBVztRQUN6QixNQUFNLElBQUltQyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtJQU5nQkQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemFjaGFcXE9uZURyaXZlXFxEZXNrdG9wXFxVbmlWaWJlXFxVbmlWaWJlLXByb2plY3QtdHJcXGxpYlxcYXV0aC1jb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBVc2VyIH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHsgY3JlYXRlU3VwYWJhc2VCcm93c2VyQ2xpZW50LCBnZXRVc2VyUHJvZmlsZSwgVXNlclJvbGUgfSBmcm9tICcuL3N1cGFiYXNlJ1xuXG5pbnRlcmZhY2UgVXNlclByb2ZpbGUge1xuICBpZDogc3RyaW5nXG4gIGVtYWlsOiBzdHJpbmdcbiAgZnVsbF9uYW1lOiBzdHJpbmcgfCBudWxsXG4gIGF2YXRhcl91cmw6IHN0cmluZyB8IG51bGxcbiAgcm9sZTogVXNlclJvbGVcbiAgdW5pdmVyc2l0eTogc3RyaW5nIHwgbnVsbFxuICBjcmVhdGVkX2F0OiBzdHJpbmcgfCBudWxsXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZyB8IG51bGxcbn1cblxuaW50ZXJmYWNlIEF1dGhDb250ZXh0VHlwZSB7XG4gIHVzZXI6IFVzZXIgfCBudWxsXG4gIHByb2ZpbGU6IFVzZXJQcm9maWxlIHwgbnVsbFxuICBsb2FkaW5nOiBib29sZWFuXG4gIHNpZ25PdXQ6ICgpID0+IFByb21pc2U8dm9pZD5cbiAgcmVmcmVzaFByb2ZpbGU6ICgpID0+IFByb21pc2U8dm9pZD5cbn1cblxuY29uc3QgQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEF1dGhDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgZnVuY3Rpb24gQXV0aFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlciB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtwcm9maWxlLCBzZXRQcm9maWxlXSA9IHVzZVN0YXRlPFVzZXJQcm9maWxlIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTdXBhYmFzZUJyb3dzZXJDbGllbnQoKVxuXG4gIGNvbnN0IHJlZnJlc2hQcm9maWxlID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGE6IHsgdXNlcjogY3VycmVudFVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICAgIGlmIChjdXJyZW50VXNlcikge1xuICAgICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBnZXRVc2VyUHJvZmlsZShjdXJyZW50VXNlci5pZClcbiAgICAgICAgaWYgKGRhdGEgJiYgIWVycm9yKSB7XG4gICAgICAgICAgc2V0UHJvZmlsZShkYXRhIGFzIFVzZXJQcm9maWxlKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlZnJlc2hpbmcgcHJvZmlsZTonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBjb25zdCBzaWduT3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKVxuICAgICAgaWYgKCFlcnJvcikge1xuICAgICAgICBzZXRVc2VyKG51bGwpXG4gICAgICAgIHNldFByb2ZpbGUobnVsbClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignU2lnbiBvdXQgZXJyb3I6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsZXQgbW91bnRlZCA9IHRydWVcblxuICAgIGNvbnN0IGluaXRpYWxpemVBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ0luaXRpYWxpemluZyBhdXRoLi4uJylcbiAgICAgICAgLy8gR2V0IGluaXRpYWwgc2Vzc2lvblxuICAgICAgICBjb25zdCB7IGRhdGE6IHsgc2Vzc2lvbiB9LCBlcnJvcjogc2Vzc2lvbkVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFNlc3Npb24oKVxuXG4gICAgICAgIGlmIChzZXNzaW9uRXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdTZXNzaW9uIGVycm9yOicsIHNlc3Npb25FcnJvcilcbiAgICAgICAgICBpZiAobW91bnRlZCkge1xuICAgICAgICAgICAgc2V0VXNlcihudWxsKVxuICAgICAgICAgICAgc2V0UHJvZmlsZShudWxsKVxuICAgICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIW1vdW50ZWQpIHJldHVyblxuXG4gICAgICAgIGNvbnNvbGUubG9nKCdTZXNzaW9uOicsIHNlc3Npb24/LnVzZXIgPyAnVXNlciBmb3VuZCcgOiAnTm8gdXNlcicpXG4gICAgICAgIHNldFVzZXIoc2Vzc2lvbj8udXNlciA/PyBudWxsKVxuXG4gICAgICAgIC8vIEdldCB1c2VyIHByb2ZpbGUgaWYgdXNlciBleGlzdHNcbiAgICAgICAgaWYgKHNlc3Npb24/LnVzZXIpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgZ2V0VXNlclByb2ZpbGUoc2Vzc2lvbi51c2VyLmlkKVxuICAgICAgICAgICAgaWYgKGRhdGEgJiYgIWVycm9yICYmIG1vdW50ZWQpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1Byb2ZpbGUgbG9hZGVkOicsIGRhdGEuZnVsbF9uYW1lKVxuICAgICAgICAgICAgICBzZXRQcm9maWxlKGRhdGEgYXMgVXNlclByb2ZpbGUpXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Byb2ZpbGUgZXJyb3I6JywgZXJyb3IpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBjYXRjaCAocHJvZmlsZUVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdQcm9maWxlIGVycm9yOicsIHByb2ZpbGVFcnJvcilcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2V0UHJvZmlsZShudWxsKVxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKG1vdW50ZWQpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBpbml0aWFsaXphdGlvbiBjb21wbGV0ZScpXG4gICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQXV0aCBpbml0aWFsaXphdGlvbiBlcnJvcjonLCBlcnJvcilcbiAgICAgICAgaWYgKG1vdW50ZWQpIHtcbiAgICAgICAgICBzZXRVc2VyKG51bGwpXG4gICAgICAgICAgc2V0UHJvZmlsZShudWxsKVxuICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICBpbml0aWFsaXplQXV0aCgpXG5cbiAgICAvLyBMaXN0ZW4gZm9yIGF1dGggY2hhbmdlc1xuICAgIGNvbnN0IHsgZGF0YTogeyBzdWJzY3JpcHRpb24gfSB9ID0gc3VwYWJhc2UuYXV0aC5vbkF1dGhTdGF0ZUNoYW5nZShcbiAgICAgIGFzeW5jIChldmVudCwgc2Vzc2lvbikgPT4ge1xuICAgICAgICBpZiAoIW1vdW50ZWQpIHJldHVyblxuXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0YXRlIGNoYW5nZWQ6JywgZXZlbnQpXG4gICAgICAgIHNldFVzZXIoc2Vzc2lvbj8udXNlciA/PyBudWxsKVxuXG4gICAgICAgIGlmIChzZXNzaW9uPy51c2VyKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IGdldFVzZXJQcm9maWxlKHNlc3Npb24udXNlci5pZClcbiAgICAgICAgICAgIGlmIChkYXRhICYmICFlcnJvciAmJiBtb3VudGVkKSB7XG4gICAgICAgICAgICAgIHNldFByb2ZpbGUoZGF0YSBhcyBVc2VyUHJvZmlsZSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyB1c2VyIHByb2ZpbGUgb24gYXV0aCBjaGFuZ2U6JywgZXJyb3IpXG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldFByb2ZpbGUobnVsbClcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChtb3VudGVkKSB7XG4gICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgICAgfVxuICAgICAgfVxuICAgIClcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBtb3VudGVkID0gZmFsc2VcbiAgICAgIHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpXG4gICAgfVxuICB9LCBbXSlcblxuICByZXR1cm4gKFxuICAgIDxBdXRoQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17eyB1c2VyLCBwcm9maWxlLCBsb2FkaW5nLCBzaWduT3V0LCByZWZyZXNoUHJvZmlsZSB9fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0F1dGhDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBdXRoKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dClcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiY3JlYXRlU3VwYWJhc2VCcm93c2VyQ2xpZW50IiwiZ2V0VXNlclByb2ZpbGUiLCJBdXRoQ29udGV4dCIsInVuZGVmaW5lZCIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJwcm9maWxlIiwic2V0UHJvZmlsZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic3VwYWJhc2UiLCJyZWZyZXNoUHJvZmlsZSIsImRhdGEiLCJjdXJyZW50VXNlciIsImF1dGgiLCJnZXRVc2VyIiwiZXJyb3IiLCJpZCIsImNvbnNvbGUiLCJzaWduT3V0IiwibW91bnRlZCIsImluaXRpYWxpemVBdXRoIiwibG9nIiwic2Vzc2lvbiIsInNlc3Npb25FcnJvciIsImdldFNlc3Npb24iLCJmdWxsX25hbWUiLCJwcm9maWxlRXJyb3IiLCJzdWJzY3JpcHRpb24iLCJvbkF1dGhTdGF0ZUNoYW5nZSIsImV2ZW50IiwidW5zdWJzY3JpYmUiLCJQcm92aWRlciIsInZhbHVlIiwidXNlQXV0aCIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth-context.tsx\n"));

/***/ })

});